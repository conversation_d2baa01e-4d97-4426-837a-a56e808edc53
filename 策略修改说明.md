# 妖底确定买入策略修正说明

## 修改概述
根据 `金牛暴起/妖底确定选股.py` 中的原始公式，对 `策略/聚宽妖底确定买入高抛卖出策略.py` 中的买入逻辑进行了完整修正，确保与原始选股公式完全一致。

## 主要修正内容

### 1. 新增辅助函数

#### `sma_custom(series, period, weight=1)`
- **功能**: 模拟通达信的SMA函数
- **公式**: `SMA(X,N,M) = (M*X + (N-M)*Y)/N`
- **用途**: 正确计算M2指标

#### `apply_filter(condition_series, period)`
- **功能**: 模拟通达信FILTER函数
- **作用**: 避免重复信号，当条件成立时，将其后N周期内的数据置为0

### 2. 指标计算修正

#### M2指标修正
- **原错误**: 使用简单移动平均 `rolling(7).mean()`
- **修正后**: 使用自定义SMA函数 `sma_custom(positive_changes, 7, 1)`
- **原公式**: `SMA(MAX(C-REF(C,1),0),7,1)/SMA(ABS(C-REF(C,1)),7,1)*100`

#### G1条件新增
- **原缺失**: 完全没有G1条件
- **新增**: `G1 = apply_filter((M2.shift(1) < 20) & (M2 > M2.shift(1)), 5)`
- **原公式**: `FILTER(REF(M2,1)<20 AND M2>REF(M2,1),5)`

#### MACD条件修正
- **原简化**: 使用价格动量代替
- **修正后**: 使用真实MACD计算（支持talib或EMA差值方法）
- **原公式**: `MACD.MACD>-1.5`

### 3. 条件逻辑修正

#### BD波段条件
- **修正前**: 缺少G1条件和FILTER功能
- **修正后**: `FILTER((G1 AND C1>20 OR C>REF(C,1)) AND REF(QD,1),10)`

#### XG选股条件
- **修正前**: 缺少FILTER功能，MACD条件简化
- **修正后**: `FILTER(REF(QD,1) AND (QR OR C>REF(C,1)) AND MACD.MACD>-1.5,10)`

### 4. 技术改进

#### 导入优化
- 新增numpy导入
- 添加talib可用性检测
- 提供MACD计算的备用方案

#### 数据处理改进
- 增加历史数据获取量（75→100天）
- 改进pandas Series索引处理
- 增强异常处理和调试信息

## 修正后的完整逻辑流程

1. **基础指标计算**
   - C1: `((MA(C,30)-L)/MA(C,60))*200`
   - M2: `SMA(MAX(C-REF(C,1),0),7,1)/SMA(ABS(C-REF(C,1)),7,1)*100`
   - TU: `C/MA(C,40)<0.74`
   - TDJ: `(H-L)/REF(C,1)>0.05`

2. **复杂指标计算**
   - SMMA相关指标（IM, TSMMA, DIVMA, ET, TDF, NTDF）
   - YUL: `COUNT(TDJ,5)>1`
   - QD: `TU AND TDJ AND YUL`
   - QR: `CROSS(NTDF,-0.9)`

3. **过滤条件**
   - G1: `FILTER(REF(M2,1)<20 AND M2>REF(M2,1),5)`
   - BD: `FILTER((G1 AND C1>20 OR C>REF(C,1)) AND REF(QD,1),10)`
   - XG: `FILTER(REF(QD,1) AND (QR OR C>REF(C,1)) AND MACD.MACD>-1.5,10)`

4. **最终条件**
   - 妖底确定: `COUNT(XG,13)>=1 AND BD`

## 验证结果
- ✅ 所有指标计算与原公式一致
- ✅ 过滤逻辑完整实现
- ✅ MACD条件正确计算
- ✅ 代码语法检查通过
- ✅ 异常处理完善

修正后的策略现在完全符合原始妖底确定选股公式的逻辑。
