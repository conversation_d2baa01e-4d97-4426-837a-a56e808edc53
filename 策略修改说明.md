# 妖底确定买入高抛卖出策略修正说明

## 修改概述

根据原始选股公式，对 `策略/聚宽妖底确定买入高抛卖出策略.py` 进行了完整修正：

1. **买入逻辑修正**: 根据 `金牛暴起/妖底确定选股.py` 修正买入策略
2. **卖出逻辑修正**: 根据 `高抛低吸/卖出选股.py` 修正卖出策略

确保策略与原始选股公式完全一致。

## 主要修正内容

### 1. 新增辅助函数

#### `sma_custom(series, period, weight=1)`
- **功能**: 模拟通达信的SMA函数
- **公式**: `SMA(X,N,M) = (M*X + (N-M)*Y)/N`
- **用途**: 正确计算M2指标

#### `apply_filter(condition_series, period)`
- **功能**: 模拟通达信FILTER函数
- **作用**: 避免重复信号，当条件成立时，将其后N周期内的数据置为0

### 2. 指标计算修正

#### M2指标修正
- **原错误**: 使用简单移动平均 `rolling(7).mean()`
- **修正后**: 使用自定义SMA函数 `sma_custom(positive_changes, 7, 1)`
- **原公式**: `SMA(MAX(C-REF(C,1),0),7,1)/SMA(ABS(C-REF(C,1)),7,1)*100`

#### G1条件新增
- **原缺失**: 完全没有G1条件
- **新增**: `G1 = apply_filter((M2.shift(1) < 20) & (M2 > M2.shift(1)), 5)`
- **原公式**: `FILTER(REF(M2,1)<20 AND M2>REF(M2,1),5)`

#### MACD条件修正
- **原简化**: 使用价格动量代替
- **修正后**: 使用真实MACD计算（支持talib或EMA差值方法）
- **原公式**: `MACD.MACD>-1.5`

### 3. 条件逻辑修正

#### BD波段条件
- **修正前**: 缺少G1条件和FILTER功能
- **修正后**: `FILTER((G1 AND C1>20 OR C>REF(C,1)) AND REF(QD,1),10)`

#### XG选股条件
- **修正前**: 缺少FILTER功能，MACD条件简化
- **修正后**: `FILTER(REF(QD,1) AND (QR OR C>REF(C,1)) AND MACD.MACD>-1.5,10)`

### 4. 技术改进

#### 导入优化
- 新增numpy导入
- 添加talib可用性检测
- 提供MACD计算的备用方案

#### 数据处理改进
- 增加历史数据获取量（75→100天）
- 改进pandas Series索引处理
- 增强异常处理和调试信息

## 修正后的完整逻辑流程

1. **基础指标计算**
   - C1: `((MA(C,30)-L)/MA(C,60))*200`
   - M2: `SMA(MAX(C-REF(C,1),0),7,1)/SMA(ABS(C-REF(C,1)),7,1)*100`
   - TU: `C/MA(C,40)<0.74`
   - TDJ: `(H-L)/REF(C,1)>0.05`

2. **复杂指标计算**
   - SMMA相关指标（IM, TSMMA, DIVMA, ET, TDF, NTDF）
   - YUL: `COUNT(TDJ,5)>1`
   - QD: `TU AND TDJ AND YUL`
   - QR: `CROSS(NTDF,-0.9)`

3. **过滤条件**
   - G1: `FILTER(REF(M2,1)<20 AND M2>REF(M2,1),5)`
   - BD: `FILTER((G1 AND C1>20 OR C>REF(C,1)) AND REF(QD,1),10)`
   - XG: `FILTER(REF(QD,1) AND (QR OR C>REF(C,1)) AND MACD.MACD>-1.5,10)`

4. **最终条件**
   - 妖底确定: `COUNT(XG,13)>=1 AND BD`

## 卖出策略修正内容

### 1. SMA函数计算修正

#### VAR1指标修正
- **原错误**: 使用简单移动平均 `rolling().mean()`
- **修正后**: 使用自定义SMA函数 `sma_custom(base_indicator, 20, 1)` 和 `sma_custom(sma1, 15, 1)`
- **原公式**: `3*SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1)-2*SMA(SMA(...),15,1)`

#### VAR3指标修正
- **原错误**: 使用简单移动平均 `VAR2.rolling(3).mean().rolling(3).mean()`
- **修正后**: 使用自定义SMA函数 `sma_custom(sma_custom(VAR2, 3, 1), 3, 1)`
- **原公式**: `SMA(SMA(VAR2,3,1),3,1)`

### 2. 完整指标体系实现

按照卖出选股公式，完整实现了19个步骤的指标计算：

1. **VAR1**: 复合SMA指标
2. **VAR2**: KDJ类型指标
3. **VAR3**: 双重SMA平滑
4. **VAR4**: EMA平滑
5. **VAR5-VAR10**: 价格区间指标
6. **VAR2W-M1**: 威廉指标系列
7. **MB1/MG1**: 交叉条件
8. **MJ**: 核心秘籍指标
9. **TM**: 探秘指标

### 3. 卖出条件精确实现

- **原公式**: `CROSS(80,MJ)` - 80从下方向上穿越MJ
- **实现**: `(MJ.shift(1) > 80) & (MJ <= 80)` - MJ从上方向下跌破80
- **逻辑**: 当MJ指标从80以上跌破80时触发卖出信号

## 验证结果

### 买入策略
- ✅ 所有15个指标计算与妖底确定选股公式一致
- ✅ G1过滤条件完整实现
- ✅ FILTER函数正确模拟
- ✅ MACD条件正确计算

### 卖出策略
- ✅ 所有19个指标计算与卖出选股公式一致
- ✅ SMA函数计算修正完成
- ✅ 核心MJ指标计算准确
- ✅ CROSS(80,MJ)条件正确实现

### 技术验证
- ✅ 代码语法检查通过
- ✅ 异常处理完善
- ✅ 调试信息详细

## 策略交易逻辑优化

### 最新修改内容（2025-07-03）

#### 1. 移除购买限制
- **修改前**: 限制单次最多买入3只股票
- **修改后**: 移除购买数量限制，允许无限制买入
- **影响**: 策略可以更充分地利用市场机会

#### 2. 支持加仓逻辑
- **修改前**: 已持有股票跳过买入检查
- **修改后**: 已持有股票如触发买入信号可继续加仓
- **实现**: 每次加仓仍按1/10仓位执行
- **卖出**: 检测到卖出信号时全仓卖出

#### 3. 交易时间调整
- **修改前**: 收盘前10分钟(14:50)执行交易
- **修改后**: 收盘前5分钟(14:55)执行交易
- **优势**: 更接近收盘，减少价格波动影响

#### 4. 代码逻辑优化
- 移除 `g.max_positions` 最大持仓限制
- 优化买入候选标记，区分新买入和加仓
- 改进日志输出，明确显示买入/加仓操作
- 增强资金管理，确保每次操作都有足够资金

### 策略特点总结

1. **买入策略**: 完全按照妖底确定选股公式执行
2. **卖出策略**: 完全按照高抛低吸卖出公式执行
3. **仓位管理**: 每次买入/加仓1/10仓位，卖出时全仓清空
4. **交易时间**: 收盘前5分钟统一执行买卖检查
5. **持仓管理**: 无持仓数量限制，支持加仓操作

修正后的策略现在完全符合原始选股公式的逻辑，买入和卖出信号都与原始公式保持一致，同时具备更灵活的仓位管理能力。
